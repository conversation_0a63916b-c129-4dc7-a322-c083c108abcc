import numpy
from setuptools import setup, Extension, find_packages

# Get the long description from the README file
with open("readme.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# Define Python dependencies from pyproject.toml
# Dependencies and other metadata like name, version, python_requires
# will be sourced from pyproject.toml by setuptools.

ext_modules = [
    Extension(
        name="threeddfa.FaceBoxes.utils.nms.cpu_nms",
        sources=["threeddfa/FaceBoxes/utils/nms/cpu_nms.pyx"],
        include_dirs=[numpy.get_include()],
        extra_compile_args=[
            "-Wno-cpp",
            "-Wno-unused-function",
        ],  # from original build.py
    ),
    Extension(
        name="threeddfa.Sim3DR.Sim3DR_Cython",
        sources=[
            "threeddfa/Sim3DR/lib/rasterize.pyx",
            "threeddfa/Sim3DR/lib/rasterize_kernel.cpp",
        ],
        language="c++",
        include_dirs=[numpy.get_include()],
        extra_compile_args=["-std=c++11"],  # from original setup.py
    ),
    Extension(
        name="threeddfa.utils.asset.render",
        sources=["threeddfa/utils/asset/render.c"],
        include_dirs=[numpy.get_include()],
        extra_compile_args=["-Wall", "-O3", "-fPIC"],
    ),
]





setup(
    # name, version, install_requires, python_requires are defined in pyproject.toml
    # and will be used automatically by setuptools.
    author="Pawan Sharma / Original Author",
    author_email="<EMAIL>",
    description="3DDFA_V2 with dependencies packaged",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/pawans-ht/3DDFA_V2_Dep",
    packages=find_packages(
        exclude=[
            "configs",
            "docs",
            "examples",
            "FaceBoxes.weights",  # More specific exclusion
            "Sim3DR.tests",  # More specific exclusion
            "weights",
            "build",
            "*.tests",
            "*.tests.*",
            "tests.*",
            "tests",
        ]
    ),
    ext_modules=ext_modules,
    # python_requires and install_requires are sourced from pyproject.toml
    include_package_data=True,  # To include non-code files specified in MANIFEST.in (if any)
    # package_data has been removed as MANIFEST.in will handle this.
    classifiers=[  # Placeholder classifiers - PLEASE UPDATE
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",  # Assuming MIT from LICENSE file
        "Operating System :: OS Independent",
    ],
    zip_safe=False,  # Good practice for packages with C extensions
)
