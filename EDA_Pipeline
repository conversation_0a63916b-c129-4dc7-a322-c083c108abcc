import os
import glob
import numpy as np
import matplotlib.pyplot as plt
import logging

from omegaconf import OmegaConf
from src.modules.affine_transform.affine_processor import AffineProcessor
from src.modules.ls_affine.image_processor import VideoProcessor
from src.utils.video_utils import read_video, write_video_ffmpeg

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# There are two methods to perform affine transformation
# 1. using AffineProcessor (Just like in src/affine.py), this would also need to return landmarks and roll angles
# 2. using VideoProcessor (Implemented in in src/modules/ls_affine/image_processor.py), this would need to use AffineProcessor's calculate_angles method to get the roll angles and landmarks
# We will use both as we want to compare the two.

class AffineMethodOne:
    """
    Affine transformation using AffineProcessor directly.
    This method uses the AffineProcessor's __call__ method to get affine transformed frames.
    """

    def __init__(self, video_path: str, device: str):
        self.video_path = video_path
        self.device = device

        # Load configuration
        CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")

        # Initialize AffineProcessor with annotation enabled
        self.affine_processor = AffineProcessor(
            config=CONFIG,
            det_config=CONFIG.detector.det_config,
            use_onnx=CONFIG.detector.use_onnx,
            device=device,
            debug_mode=False,
            annotation=True,  # Enable annotation for visualization
        )

    def __enter__(self):
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Clean up resources when exiting context manager."""
        self.affine_processor.reset_state()
        return False

    def run(self) -> tuple[np.ndarray, list[float]]:
        """
        Apply affine transformation using AffineProcessor.

        Returns:
            Tuple[np.ndarray, List[float]]: Transformed video frames and roll angles
        """
        try:
            logger.info(f"Processing video with AffineMethodOne: {self.video_path}")

            # Read video frames
            video_frames = read_video(self.video_path, change_fps=False)

            # Get landmarks and roll angles first
            _, roll_angles = self.affine_processor.calculate_angles(video_frames=video_frames)

            # Reset processor state before transformation
            self.affine_processor.reset_state()

            # Re-initialize with annotation for transformation
            CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")
            self.affine_processor = AffineProcessor(
                config=CONFIG,
                det_config=CONFIG.detector.det_config,
                use_onnx=CONFIG.detector.use_onnx,
                device=self.device,
                debug_mode=False,
                annotation=True,
            )

            # Apply affine transformation
            result = self.affine_processor(
                video_frames=video_frames,
                return_boxes=True
            )

            # Handle different return formats from AffineProcessor
            if result is None:
                raise ValueError("AffineProcessor returned None")

            if len(result) == 3:
                transformed_frames, _, _ = result
            elif len(result) == 2:
                transformed_frames, _ = result
            else:
                raise ValueError(f"Unexpected return format from AffineProcessor: {len(result)} items")

            # Convert tensor to numpy if needed
            import torch
            if torch.is_tensor(transformed_frames):
                transformed_frames = transformed_frames.cpu().numpy()
            elif isinstance(transformed_frames, list):
                transformed_frames = np.array(transformed_frames)

            # Ensure frames are in correct format (H, W, C) and uint8
            if transformed_frames.ndim == 4 and transformed_frames.shape[1] == 3:
                # Convert from (N, C, H, W) to (N, H, W, C)
                transformed_frames = np.transpose(transformed_frames, (0, 2, 3, 1))

            # Convert to uint8 if needed
            if transformed_frames.dtype != np.uint8:
                if np.max(transformed_frames) <= 1.0:
                    transformed_frames = (transformed_frames * 255).astype(np.uint8)
                else:
                    transformed_frames = transformed_frames.astype(np.uint8)

            logger.info(f"AffineMethodOne completed. Frames shape: {transformed_frames.shape}")
            return transformed_frames, roll_angles or []

        except Exception as e:
            logger.error(f"Error in AffineMethodOne: {e}")
            raise


class AffineMethodTwo:
    """
    Affine transformation using VideoProcessor with AffineProcessor for angle calculation.
    This method uses VideoProcessor's affine_transform_video method combined with
    AffineProcessor's calculate_angles method.
    """

    def __init__(self, video_path: str, device: str):
        self.video_path = video_path
        self.device = device

        # Initialize VideoProcessor
        self.video_processor = VideoProcessor(resolution=256, device=device)

        # Initialize AffineProcessor for angle calculation
        CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")
        self.affine_processor = AffineProcessor(
            config=CONFIG,
            det_config=CONFIG.detector.det_config,
            use_onnx=CONFIG.detector.use_onnx,
            device=self.device,
            debug_mode=False,
            annotation=True,  # Enable annotation for visualization
        )

    def __enter__(self):
        return self

    def __exit__(self, _exc_type, _exc_val, _exc_tb):
        """Clean up resources when exiting context manager."""
        if hasattr(self, 'affine_processor'):
            self.affine_processor.reset_state()
        return False

    def run(self) -> tuple[np.ndarray, list[float]]:
        """
        Apply affine transformation using VideoProcessor and get angles from AffineProcessor.

        Returns:
            Tuple[np.ndarray, List[float]]: Transformed video frames and roll angles
        """
        try:
            logger.info(f"Processing video with AffineMethodTwo: {self.video_path}")

            # Read video frames
            video_frames = read_video(self.video_path, change_fps=False)

            # Get roll angles using AffineProcessor
            _, roll_angles = self.affine_processor.calculate_angles(video_frames=video_frames)

            # Apply affine transformation using VideoProcessor
            transformed_frames = self.video_processor.affine_transform_video(self.video_path)

            # Ensure frames are in correct format
            if isinstance(transformed_frames, np.ndarray):
                # VideoProcessor returns frames in (N, H, W, C) format
                if transformed_frames.dtype != np.uint8:
                    if np.max(transformed_frames) <= 1.0:
                        transformed_frames = (transformed_frames * 255).astype(np.uint8)
                    else:
                        transformed_frames = transformed_frames.astype(np.uint8)
            else:
                raise ValueError(f"Unexpected frame format from VideoProcessor: {type(transformed_frames)}")

            logger.info(f"AffineMethodTwo completed. Frames shape: {transformed_frames.shape}")
            return transformed_frames, roll_angles or []

        except Exception as e:
            logger.error(f"Error in AffineMethodTwo: {e}")
            raise
        


class EDAPipeline:
    """
    Exploratory Data Analysis Pipeline for comparing two affine transformation methods.

    This class processes videos using both AffineProcessor and VideoProcessor methods,
    saves the results with appropriate naming conventions, and generates comparison plots.
    """

    def __init__(self, data_dir: str, device: str = "cuda"):
        """
        Initialize the EDA Pipeline.

        Args:
            data_dir (str): Directory containing input videos
            device (str): Device to use for processing ('cuda' or 'cpu')
        """
        self.data_dir = data_dir
        self.device = device
        self.output_dir = os.path.join(data_dir, "eda_output")
        self.plots_dir = os.path.join(self.output_dir, "plots")

        # Create output directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.plots_dir, exist_ok=True)

        logger.info(f"EDAPipeline initialized with data_dir: {data_dir}, device: {device}")

    def _get_video_files(self) -> list[str]:
        """
        Get all video files from the data directory.

        Returns:
            List[str]: List of video file paths
        """
        video_extensions = ['*.mp4', '*.avi', '*.mov', '*.mkv', '*.webm']
        video_files = []

        for ext in video_extensions:
            video_files.extend(glob.glob(os.path.join(self.data_dir, ext)))
            video_files.extend(glob.glob(os.path.join(self.data_dir, "**", ext), recursive=True))

        logger.info(f"Found {len(video_files)} video files")
        return video_files

    def plot_roll_angles(self, roll_angles_method1: list[float], roll_angles_method2: list[float],
                        video_name: str, save_path: str | None = None) -> str:
        """
        Plot and compare roll angles from both methods.

        Args:
            roll_angles_method1 (List[float]): Roll angles from AffineProcessor method
            roll_angles_method2 (List[float]): Roll angles from VideoProcessor method
            video_name (str): Name of the video being processed
            save_path (Optional[str]): Path to save the plot

        Returns:
            str: Path where the plot was saved
        """
        try:
            plt.figure(figsize=(12, 8))

            frames = range(len(roll_angles_method1))

            # Plot both methods
            plt.subplot(2, 1, 1)
            plt.plot(frames, roll_angles_method1, 'b-', label='AffineProcessor Method', linewidth=2)
            plt.plot(frames, roll_angles_method2, 'r--', label='VideoProcessor Method', linewidth=2)
            plt.xlabel('Frame Number')
            plt.ylabel('Roll Angle (radians)')
            plt.title(f'Roll Angle Comparison - {video_name}')
            plt.legend()
            plt.grid(True, alpha=0.3)

            # Plot difference
            plt.subplot(2, 1, 2)
            if len(roll_angles_method1) == len(roll_angles_method2):
                diff = np.array(roll_angles_method1) - np.array(roll_angles_method2)
                plt.plot(frames, diff, 'g-', linewidth=2)
                plt.xlabel('Frame Number')
                plt.ylabel('Angle Difference (radians)')
                plt.title('Difference between Methods (Method1 - Method2)')
                plt.grid(True, alpha=0.3)

                # Add statistics
                mean_diff = np.mean(np.abs(diff))
                max_diff = np.max(np.abs(diff))
                plt.text(0.02, 0.98, f'Mean Abs Diff: {mean_diff:.4f}\nMax Abs Diff: {max_diff:.4f}',
                        transform=plt.gca().transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            plt.tight_layout()

            # Save plot
            if save_path is None:
                save_path = os.path.join(self.plots_dir, f"{video_name}_roll_angles_comparison.png")

            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"Roll angles plot saved to: {save_path}")
            return save_path

        except Exception as e:
            logger.error(f"Error plotting roll angles: {e}")
            raise

    def run_eda(self) -> None:
        """
        Run the complete EDA pipeline on all videos in the data directory.

        This method:
        1. Processes each video with both affine transformation methods
        2. Saves results with HD_ and LS_ prefixes
        3. Generates comparison plots for roll angles
        4. Creates a summary report
        """
        video_files = self._get_video_files()

        if not video_files:
            logger.warning("No video files found in the data directory")
            return

        summary_data = []

        for video_path in video_files:
            try:
                video_name = os.path.splitext(os.path.basename(video_path))[0]
                logger.info(f"Processing video: {video_name}")

                # Method 1: AffineProcessor
                logger.info("Running AffineMethodOne...")
                with AffineMethodOne(video_path, self.device) as method1:
                    frames_method1, roll_angles_method1 = method1.run()

                # Save Method 1 results
                output_path_method1 = os.path.join(self.output_dir, f"HD_{video_name}.mp4")
                write_video_ffmpeg(output_path_method1, frames_method1, fps=25)
                logger.info(f"Saved HD video: {output_path_method1}")

                # Method 2: VideoProcessor
                logger.info("Running AffineMethodTwo...")
                with AffineMethodTwo(video_path, self.device) as method2:
                    frames_method2, roll_angles_method2 = method2.run()

                # Save Method 2 results
                output_path_method2 = os.path.join(self.output_dir, f"LS_{video_name}.mp4")
                write_video_ffmpeg(output_path_method2, frames_method2, fps=25)
                logger.info(f"Saved LS video: {output_path_method2}")

                # Generate comparison plots
                if roll_angles_method1 and roll_angles_method2:
                    plot_path = self.plot_roll_angles(
                        roll_angles_method1,
                        roll_angles_method2,
                        video_name
                    )

                    # Calculate statistics for summary
                    if len(roll_angles_method1) == len(roll_angles_method2):
                        diff = np.array(roll_angles_method1) - np.array(roll_angles_method2)
                        mean_abs_diff = np.mean(np.abs(diff))
                        max_abs_diff = np.max(np.abs(diff))
                        std_diff = np.std(diff)
                    else:
                        mean_abs_diff = max_abs_diff = std_diff = None

                    summary_data.append({
                        'video_name': video_name,
                        'frames_count': len(frames_method1),
                        'method1_angles_count': len(roll_angles_method1),
                        'method2_angles_count': len(roll_angles_method2),
                        'mean_abs_diff': mean_abs_diff,
                        'max_abs_diff': max_abs_diff,
                        'std_diff': std_diff,
                        'hd_output': output_path_method1,
                        'ls_output': output_path_method2,
                        'plot_path': plot_path
                    })
                else:
                    logger.warning(f"No roll angles available for {video_name}")
                    summary_data.append({
                        'video_name': video_name,
                        'frames_count': len(frames_method1),
                        'method1_angles_count': 0,
                        'method2_angles_count': 0,
                        'mean_abs_diff': None,
                        'max_abs_diff': None,
                        'std_diff': None,
                        'hd_output': output_path_method1,
                        'ls_output': output_path_method2,
                        'plot_path': None
                    })

                logger.info(f"Completed processing: {video_name}")

            except Exception as e:
                logger.error(f"Error processing video {video_path}: {e}")
                continue

        # Generate summary report
        self._generate_summary_report(summary_data)
        logger.info("EDA pipeline completed successfully!")

    def _generate_summary_report(self, summary_data: list) -> None:
        """
        Generate a summary report of the EDA results.

        Args:
            summary_data (list): List of dictionaries containing processing results
        """
        try:
            report_path = os.path.join(self.output_dir, "eda_summary_report.txt")

            with open(report_path, 'w') as f:
                f.write("EDA Pipeline Summary Report\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"Total videos processed: {len(summary_data)}\n")
                f.write(f"Output directory: {self.output_dir}\n")
                f.write(f"Device used: {self.device}\n\n")

                # Overall statistics
                valid_diffs = [item['mean_abs_diff'] for item in summary_data if item['mean_abs_diff'] is not None]
                if valid_diffs:
                    f.write("Overall Statistics:\n")
                    f.write(f"  Average mean absolute difference: {np.mean(valid_diffs):.6f}\n")
                    f.write(f"  Standard deviation of differences: {np.std(valid_diffs):.6f}\n")
                    f.write(f"  Min mean absolute difference: {np.min(valid_diffs):.6f}\n")
                    f.write(f"  Max mean absolute difference: {np.max(valid_diffs):.6f}\n\n")

                # Individual video results
                f.write("Individual Video Results:\n")
                f.write("-" * 30 + "\n")

                for item in summary_data:
                    f.write(f"\nVideo: {item['video_name']}\n")
                    f.write(f"  Frames processed: {item['frames_count']}\n")
                    f.write(f"  Method1 angles: {item['method1_angles_count']}\n")
                    f.write(f"  Method2 angles: {item['method2_angles_count']}\n")

                    if item['mean_abs_diff'] is not None:
                        f.write(f"  Mean absolute difference: {item['mean_abs_diff']:.6f}\n")
                        f.write(f"  Max absolute difference: {item['max_abs_diff']:.6f}\n")
                        f.write(f"  Standard deviation: {item['std_diff']:.6f}\n")
                    else:
                        f.write("  No angle comparison available\n")

                    f.write(f"  HD output: {item['hd_output']}\n")
                    f.write(f"  LS output: {item['ls_output']}\n")
                    if item['plot_path']:
                        f.write(f"  Plot: {item['plot_path']}\n")

            logger.info(f"Summary report saved to: {report_path}")

        except Exception as e:
            logger.error(f"Error generating summary report: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Run EDA Pipeline for Affine Transformation Comparison")
    parser.add_argument("--data_dir", type=str, required=True,
                       help="Directory containing input videos")
    parser.add_argument("--device", type=str, default="cuda",
                       help="Device to use for processing (cuda/cpu)")

    args = parser.parse_args()

    # Run the EDA pipeline
    pipeline = EDAPipeline(args.data_dir, args.device)
    pipeline.run_eda()


