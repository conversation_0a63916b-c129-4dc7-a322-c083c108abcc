from omegaconf import OmegaConf
from src.modules.affine_transform.affine_processor import AffineProcessor

# There are two methods to perform affine transformation
# 1. using AffineProcessor (Just like in src/affine.py), this would also need to return landmarks and roll angles
# 2. using VideoProcessor (Implemented in in src/modules/ls_affine/image_processor.py), this would need to use AffineProcessor's calculate_angles method to get the roll angles and landmarks
# We will use both as we want to compare the two.

class AffineMethodOne:
    def __init__(self, video_path: str, device: str):
        self.video_path = video_path

    def __enter__(self):
        return self

    def __exit__(self):
        self.affine_processor.reset_state()

    def run(self):
        # Method to affine videos using AffineProcessor
        # Use AffineProcessor's call method to get the affine transformed frames
        # Return the affine transformed video and roll angles
        pass

class AffineMethodTwo:
    def __init__(self, video_path: str, device: str):
        self.video_path = video_path

    def __enter__(self):
        return self
    
    def __exit__(self):
        self.affine_processor.reset_state()

    def run(self):
        # Method to affine videos using VideoProcessor
        # Call AffineProcessor's calculate_angles method to get roll angles and landmarks with annotation on
        # Use VideoProcessor's affine_transform_video method to get the affine transformed frames
        # Return the affine transformed video and roll angles
        pass
        


class EDAPipeline:
    def __init__(self, data_dir: str):
        self.data_dir = data_dir
        
        CONFIG = OmegaConf.load("src/modules/affine_transform/config.yaml")
        self.affine_processor = AffineProcessor(
            config=CONFIG,
            det_config=CONFIG.detector.det_config,
            use_onnx=CONFIG.detector.use_onnx,
            device=device,
            debug_mode=False,
            annotation=True,
        )

    def plot_roll_angles(self):
        # Plot the roll angles across frames
        pass

    def run_eda(self):
        # Method to run EDA on the videos
        # Run AffineMethod1 on the video -> Save it as HD_{video_name}
        # Run AffineMethod2 on the video -> Save it as LS_{video_name}
        # Both videos should be annotated
        # Plot the roll angles for both methods
        # Save the plots
    
        pass


